from aiogram import Router, F
from aiogram.types import CallbackQuery, Message
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.filters import StateFilter

from database import SubjectRepository, MicrotopicRepository
from common.keyboards import get_home_kb
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

router = Router()

class ManagerMicrotopicsStates(StatesGroup):
    # Состояния для добавления микротемы
    select_subject_for_microtopic = State()
    enter_microtopic_name = State()
    confirm_add_microtopic = State()
    
    # Состояния для удаления микротемы
    select_subject_for_deletion = State()
    select_microtopic_to_delete = State()
    confirm_delete_microtopic = State()

# === ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ ===

async def get_subjects_kb(callback_prefix: str = "subject") -> InlineKeyboardMarkup:
    """Клавиатура со списком предметов"""
    subjects = await SubjectRepository.get_all()
    
    buttons = []
    for subject in subjects:
        buttons.append([
            InlineKeyboardButton(
                text=subject.name,
                callback_data=f"{callback_prefix}_{subject.id}"
            )
        ])
    
    buttons.append([InlineKeyboardButton(text="🏠 Главное меню", callback_data="back_to_main")])
    return InlineKeyboardMarkup(inline_keyboard=buttons)

async def get_microtopics_kb(subject_id: int, callback_prefix: str = "microtopic") -> InlineKeyboardMarkup:
    """Клавиатура со списком микротем для предмета"""
    microtopics = await MicrotopicRepository.get_by_subject(subject_id)
    
    buttons = []
    if not microtopics:
        buttons.append([
            InlineKeyboardButton(text="📝 Микротем пока нет", callback_data="no_microtopics")
        ])
    else:
        for microtopic in microtopics:
            buttons.append([
                InlineKeyboardButton(
                    text=microtopic.name,
                    callback_data=f"{callback_prefix}_{microtopic.id}"
                )
            ])
    
    buttons.append([InlineKeyboardButton(text="🏠 Главное меню", callback_data="back_to_main")])
    return InlineKeyboardMarkup(inline_keyboard=buttons)

def get_confirmation_kb(action: str, entity: str, entity_id: str = "") -> InlineKeyboardMarkup:
    """Клавиатура подтверждения действия"""
    buttons = [
        [
            InlineKeyboardButton(
                text="✅ Подтвердить",
                callback_data=f"confirm_{action}_{entity}_{entity_id}"
            ),
            InlineKeyboardButton(
                text="❌ Отменить",
                callback_data=f"cancel_{action}_{entity}"
            )
        ],
        [InlineKeyboardButton(text="🏠 Главное меню", callback_data="back_to_main")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=buttons)

# === ДОБАВЛЕНИЕ МИКРОТЕМЫ ===

@router.callback_query(F.data == "add_microtopic")
async def start_add_microtopic(callback: CallbackQuery, state: FSMContext):
    """Начать добавление микротемы"""
    await callback.message.edit_text(
        text="Выберите предмет для добавления микротемы:",
        reply_markup=await get_subjects_kb("microtopic_subject")
    )
    await state.set_state(ManagerMicrotopicsStates.select_subject_for_microtopic)

@router.callback_query(ManagerMicrotopicsStates.select_subject_for_microtopic, F.data.startswith("microtopic_subject_"))
async def select_subject_for_microtopic(callback: CallbackQuery, state: FSMContext):
    """Выбрать предмет для микротемы"""
    subject_id = int(callback.data.replace("microtopic_subject_", ""))
    
    # Получаем информацию о предмете
    subject = await SubjectRepository.get_by_id(subject_id)
    if not subject:
        await callback.message.edit_text(
            text="❌ Предмет не найден!",
            reply_markup=get_home_kb()
        )
        return
    
    await state.update_data(subject_id=subject_id, subject_name=subject.name)
    await state.set_state(ManagerMicrotopicsStates.enter_microtopic_name)
    
    await callback.message.edit_text(
        text=f"Предмет: {subject.name}\n\nВведите название микротемы:",
        reply_markup=get_home_kb()
    )

@router.message(StateFilter(ManagerMicrotopicsStates.enter_microtopic_name))
async def process_microtopic_name(message: Message, state: FSMContext):
    """Обработать ввод названия микротемы"""
    microtopic_name = message.text.strip()
    data = await state.get_data()
    subject_name = data.get("subject_name", "")
    subject_id = data.get("subject_id")
    
    # Проверяем, существует ли уже такая микротема
    exists = await MicrotopicRepository.exists(microtopic_name, subject_id)
    if exists:
        await message.answer(
            text=f"❌ Микротема '{microtopic_name}' уже существует для предмета '{subject_name}'!\n\n"
                 f"Введите другое название:",
            reply_markup=get_home_kb()
        )
        return
    
    await state.update_data(microtopic_name=microtopic_name)
    await state.set_state(ManagerMicrotopicsStates.confirm_add_microtopic)
    
    await message.answer(
        text=f"📋 Подтверждение добавления микротемы:\n\n"
             f"Предмет: {subject_name}\n"
             f"Микротема: {microtopic_name}",
        reply_markup=get_confirmation_kb("add", "microtopic")
    )

@router.callback_query(StateFilter(ManagerMicrotopicsStates.confirm_add_microtopic), F.data.startswith("confirm_add_microtopic"))
async def confirm_add_microtopic(callback: CallbackQuery, state: FSMContext):
    """Подтвердить добавление микротемы"""
    data = await state.get_data()
    subject_id = data.get("subject_id")
    subject_name = data.get("subject_name", "")
    microtopic_name = data.get("microtopic_name", "")
    
    try:
        microtopic = await MicrotopicRepository.create(microtopic_name, subject_id)
        await callback.message.edit_text(
            text=f"✅ Микротема '{microtopic.name}' успешно добавлена к предмету '{subject_name}'!",
            reply_markup=get_home_kb()
        )
    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при добавлении микротемы: {e}",
            reply_markup=get_home_kb()
        )
    
    await state.clear()

# === УДАЛЕНИЕ МИКРОТЕМЫ ===

@router.callback_query(F.data == "remove_microtopic")
async def start_remove_microtopic(callback: CallbackQuery, state: FSMContext):
    """Начать удаление микротемы"""
    await callback.message.edit_text(
        text="Выберите предмет:",
        reply_markup=await get_subjects_kb("delete_subject")
    )
    await state.set_state(ManagerMicrotopicsStates.select_subject_for_deletion)

@router.callback_query(ManagerMicrotopicsStates.select_subject_for_deletion, F.data.startswith("delete_subject_"))
async def select_subject_for_deletion(callback: CallbackQuery, state: FSMContext):
    """Выбрать предмет для удаления микротемы"""
    subject_id = int(callback.data.replace("delete_subject_", ""))
    
    # Получаем информацию о предмете
    subject = await SubjectRepository.get_by_id(subject_id)
    if not subject:
        await callback.message.edit_text(
            text="❌ Предмет не найден!",
            reply_markup=get_home_kb()
        )
        return
    
    await state.update_data(deletion_subject_id=subject_id, deletion_subject_name=subject.name)
    await state.set_state(ManagerMicrotopicsStates.select_microtopic_to_delete)
    
    await callback.message.edit_text(
        text=f"Предмет: {subject.name}\n\nВыберите микротему для удаления:",
        reply_markup=await get_microtopics_kb(subject_id, "delete_microtopic")
    )

@router.callback_query(ManagerMicrotopicsStates.select_microtopic_to_delete, F.data.startswith("delete_microtopic_"))
async def select_microtopic_to_delete(callback: CallbackQuery, state: FSMContext):
    """Выбрать микротему для удаления"""
    microtopic_id = int(callback.data.replace("delete_microtopic_", ""))
    data = await state.get_data()
    subject_name = data.get("deletion_subject_name", "")
    
    # Получаем информацию о микротеме
    microtopic = await MicrotopicRepository.get_by_id(microtopic_id)
    if not microtopic:
        await callback.message.edit_text(
            text="❌ Микротема не найдена!",
            reply_markup=get_home_kb()
        )
        return
    
    await state.update_data(microtopic_to_delete_id=microtopic_id, microtopic_to_delete_name=microtopic.name)
    await state.set_state(ManagerMicrotopicsStates.confirm_delete_microtopic)
    
    await callback.message.edit_text(
        text=f"🗑 Подтверждение удаления микротемы:\n\n"
             f"Предмет: {subject_name}\n"
             f"Микротема: {microtopic.name}\n\n"
             f"⚠️ Это действие нельзя отменить!",
        reply_markup=get_confirmation_kb("delete", "microtopic", str(microtopic_id))
    )

@router.callback_query(StateFilter(ManagerMicrotopicsStates.confirm_delete_microtopic), F.data.startswith("confirm_delete_microtopic"))
async def confirm_delete_microtopic(callback: CallbackQuery, state: FSMContext):
    """Подтвердить удаление микротемы"""
    data = await state.get_data()
    microtopic_id = data.get("microtopic_to_delete_id")
    microtopic_name = data.get("microtopic_to_delete_name", "")
    subject_name = data.get("deletion_subject_name", "")
    
    success = await MicrotopicRepository.delete(microtopic_id)
    
    if success:
        await callback.message.edit_text(
            text=f"✅ Микротема '{microtopic_name}' успешно удалена из предмета '{subject_name}'!",
            reply_markup=get_home_kb()
        )
    else:
        await callback.message.edit_text(
            text="❌ Микротема не найдена!",
            reply_markup=get_home_kb()
        )
    
    await state.clear()

# === ОТМЕНА ДЕЙСТВИЙ ===

@router.callback_query(StateFilter(ManagerMicrotopicsStates.confirm_add_microtopic), F.data.startswith("cancel_add_microtopic"))
async def cancel_add_microtopic(callback: CallbackQuery, state: FSMContext):
    """Отменить добавление микротемы"""
    await callback.message.edit_text(
        text="❌ Добавление микротемы отменено",
        reply_markup=get_home_kb()
    )
    await state.clear()

@router.callback_query(StateFilter(ManagerMicrotopicsStates.confirm_delete_microtopic), F.data.startswith("cancel_delete_microtopic"))
async def cancel_delete_microtopic(callback: CallbackQuery, state: FSMContext):
    """Отменить удаление микротемы"""
    await callback.message.edit_text(
        text="❌ Удаление микротемы отменено",
        reply_markup=get_home_kb()
    )
    await state.clear()
